# 基于官方 Playwright Docker 镜像
FROM mcr.microsoft.com/playwright:v1.55.0-noble

# 安装 VNC 服务器、NoVNC 和其他必要组件
USER root
RUN apt-get update && apt-get install -y \
    x11vnc \
    xvfb \
    fluxbox \
    wget \
    supervisor \
    sudo \
    && rm -rf /var/lib/apt/lists/*

# 安装 NoVNC
RUN mkdir -p /opt/noVNC/utils/websockify && \
    wget -qO- https://github.com/novnc/noVNC/archive/v1.4.0.tar.gz | tar xz --strip 1 -C /opt/noVNC && \
    wget -qO- https://github.com/novnc/websockify/archive/v0.10.0.tar.gz | tar xz --strip 1 -C /opt/noVNC/utils/websockify && \
    ln -s /opt/noVNC/vnc.html /opt/noVNC/index.html

# 创建必要的目录
RUN mkdir -p /var/log/supervisor /home/<USER>/.vnc

# 设置目录权限和 sudo 权限
RUN mkdir -p /home/<USER>/.vnc && \
    chown -R pwuser:pwuser /home/<USER>/.vnc && \
    echo "pwuser ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# 复制配置文件
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY start-services.sh /usr/local/bin/start-services.sh
RUN chmod +x /usr/local/bin/start-services.sh

# 设置环境变量
ENV DISPLAY=:99
ENV SCREEN_WIDTH=1920
ENV SCREEN_HEIGHT=1080
ENV SCREEN_DEPTH=24
ENV VNC_PORT=5900
ENV NOVNC_PORT=6080

# 暴露端口
EXPOSE 5900 6080 3000

# 切换到 pwuser
USER pwuser
WORKDIR /home/<USER>

# 启动服务
CMD ["/usr/local/bin/start-services.sh"]
