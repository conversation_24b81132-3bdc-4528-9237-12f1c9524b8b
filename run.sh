#!/bin/bash

# Playwright Docker with VNC/NoVNC 管理脚本

case "$1" in
    start)
        echo "启动 Playwright Docker 服务..."
        docker compose up -d --build
        echo ""
        echo "服务已启动！"
        echo ""
        echo "访问地址："
        echo "实例 1 NoVNC: http://localhost:6901"
        echo "实例 2 NoVNC: http://localhost:6902"
        echo "无需密码，直接连接"
        echo ""
        echo "Playwright Server:"
        echo "实例 1: ws://localhost:3001/"
        echo "实例 2: ws://localhost:3002/"
        ;;
    stop)
        echo "停止 Playwright Docker 服务..."
        docker compose down
        echo "服务已停止！"
        ;;
    logs)
        docker compose logs -f
        ;;
    status)
        docker compose ps
        ;;
    test)
        echo "运行示例测试..."
        echo "请确保服务已启动 (./run.sh start)"
        echo "等待服务完全启动..."
        sleep 10
        docker exec playwright-vnc-1 node /home/<USER>/tests/example.js
        ;;
    google)
        echo "运行 Google 测试 (单实例)..."
        echo "请确保服务已启动 (./run.sh start)"
        echo "等待服务完全启动..."
        sleep 5
        echo "💡 提示: 你可以通过 http://localhost:6901 观察浏览器运行过程"
        docker exec playwright-vnc-1 node /home/<USER>/tests/working-google-test.js
        ;;
    dual-google)
        echo "运行 Google 测试 (双实例并行)..."
        echo "请确保服务已启动 (./run.sh start)"
        echo "等待服务完全启动..."
        sleep 5
        docker exec playwright-vnc-1 node /home/<USER>/tests/dual-google-test.js
        ;;
    python)
        echo "运行 Python Google 测试 (WebSocket 连接)..."
        echo "请确保服务已启动 (./run.sh start)"
        echo "等待服务完全启动..."
        sleep 5
        echo "💡 提示: 你可以通过 http://localhost:6901 观察浏览器运行过程"
        docker exec playwright-vnc-1 python3 /home/<USER>/tests/simple_google_ws.py
        ;;
    python-full)
        echo "运行完整 Python Google 测试..."
        echo "请确保服务已启动 (./run.sh start)"
        echo "等待服务完全启动..."
        sleep 5
        echo "💡 提示: 你可以通过 http://localhost:6901 观察浏览器运行过程"
        docker exec playwright-vnc-1 python3 /home/<USER>/tests/google_test.py
        ;;
    python-ws)
        echo "运行 Python WebSocket Google 测试..."
        echo "请确保服务已启动 (./run.sh start)"
        echo "等待服务完全启动..."
        sleep 5
        echo "💡 提示: 你可以通过 http://localhost:6901 观察浏览器运行过程"
        docker exec playwright-vnc-1 python3 /home/<USER>/tests/python_ws_google.py
        ;;
    clean)
        echo "清理 Docker 资源..."
        docker compose down -v
        docker system prune -f
        echo "清理完成！"
        ;;
    *)
        echo "用法: $0 {start|stop|logs|status|test|google|dual-google|python|python-full|python-ws|clean}"
        echo ""
        echo "命令说明："
        echo "  start       - 启动所有服务"
        echo "  stop        - 停止所有服务"
        echo "  logs        - 查看服务日志"
        echo "  status      - 查看服务状态"
        echo "  test        - 运行示例测试"
        echo "  google      - 运行 Google 测试 (JavaScript)"
        echo "  dual-google - 运行 Google 测试 (双实例并行)"
        echo "  python      - 运行 Python Google 测试 (直接启动)"
        echo "  python-full - 运行完整 Python Google 测试"
        echo "  python-ws   - 运行 Python Google 测试 (WebSocket 连接)"
        echo "  clean       - 清理 Docker 资源"
        exit 1
        ;;
esac
