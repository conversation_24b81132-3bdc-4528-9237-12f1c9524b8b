[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:xvfb]
command=/usr/bin/Xvfb :99 -screen 0 1280x800x24
autorestart=true
stdout_logfile=/var/log/supervisor/xvfb.log
stderr_logfile=/var/log/supervisor/xvfb.log

[program:fluxbox]
command=/usr/bin/fluxbox -display :99
autorestart=true
stdout_logfile=/var/log/supervisor/fluxbox.log
stderr_logfile=/var/log/supervisor/fluxbox.log
environment=DISPLAY=":99"

[program:x11vnc]
command=/usr/bin/x11vnc -forever -usepw -create -rfbauth /home/<USER>/.vnc/passwd -rfbport 5900 -display :99
autorestart=true
stdout_logfile=/var/log/supervisor/x11vnc.log
stderr_logfile=/var/log/supervisor/x11vnc.log

[program:novnc]
command=/opt/noVNC/utils/novnc_proxy --vnc localhost:5900 --listen 6080
autorestart=true
stdout_logfile=/var/log/supervisor/novnc.log
stderr_logfile=/var/log/supervisor/novnc.log

[program:playwright-server]
command=/bin/bash -c "cd /home/<USER>/home/<USER>/.npm && npx -y playwright@1.55.0 run-server --port 3000 --host 0.0.0.0"
autorestart=true
stdout_logfile=/var/log/supervisor/playwright.log
stderr_logfile=/var/log/supervisor/playwright.log
user=pwuser
environment=DISPLAY=":99",HOME="/home/<USER>"
