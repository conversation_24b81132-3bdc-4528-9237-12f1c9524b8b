version: '3.8'

services:
  playwright-instance-1:
    build: .
    container_name: playwright-vnc-1
    ports:
      - "5901:5900"    # VNC 端口
      - "6901:6080"    # NoVNC Web 界面端口
      - "3001:3000"    # Playwright Server 端口
    environment:
      - DISPLAY=:99
      - SCREEN_WIDTH=1280
      - SCREEN_HEIGHT=800
      - SCREEN_DEPTH=24
      - VNC_PORT=5900
      - NOVNC_PORT=6080
    volumes:
      - ./tests:/home/<USER>/tests
      - ./results:/home/<USER>/results
    init: true
    ipc: host
    cap_add:
      - SYS_ADMIN
    restart: unless-stopped

  playwright-instance-2:
    build: .
    container_name: playwright-vnc-2
    ports:
      - "5902:5900"    # VNC 端口
      - "6902:6080"    # NoVNC Web 界面端口
      - "3002:3000"    # Playwright Server 端口
    environment:
      - DISPLAY=:99
      - SCREEN_WIDTH=1920
      - SCREEN_HEIGHT=1080
      - SCREEN_DEPTH=24
      - VNC_PORT=5900
      - NOVNC_PORT=6080
    volumes:
      - ./tests:/home/<USER>/tests
      - ./results:/home/<USER>/results
    init: true
    ipc: host
    cap_add:
      - SYS_ADMIN
    restart: unless-stopped

networks:
  default:
    driver: bridge
