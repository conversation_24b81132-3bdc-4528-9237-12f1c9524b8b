# Playwright Docker with VNC/NoVNC

基于 Playwright 官方文档创建的 Docker Compose 配置，包含 2 个 Playwright 实例并集成 VNC/NoVNC 功能。

## 功能特性

- 2 个独立的 Playwright 实例
- 每个实例都有 VNC 服务器支持
- NoVNC Web 界面，可通过浏览器查看
- 基于官方 Playwright Docker 镜像 (v1.55.0-noble)
- 支持可视化调试和监控

## 端口配置

### 实例 1 (playwright-vnc-1)
- VNC: `localhost:5901`
- NoVNC Web 界面: `http://localhost:6901`
- Playwright Server: `localhost:3001`

### 实例 2 (playwright-vnc-2)
- VNC: `localhost:5902`
- NoVNC Web 界面: `http://localhost:6902`
- Playwright Server: `localhost:3002`

## 使用方法

### 1. 启动服务

```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d --build
```

### 2. 访问 VNC 界面

通过浏览器访问：
- 实例 1: http://localhost:6901
- 实例 2: http://localhost:6902

无需密码，直接连接

### 3. 连接到 Playwright Server

在你的测试代码中连接到远程 Playwright 服务器：

```javascript
// 连接到实例 1
const browser = await playwright.chromium.connect('ws://localhost:3001/');

// 连接到实例 2
const browser = await playwright.chromium.connect('ws://localhost:3002/');
```

或使用环境变量：

```bash
# 连接到实例 1
PW_TEST_CONNECT_WS_ENDPOINT=ws://127.0.0.1:3001/ npx playwright test

# 连接到实例 2
PW_TEST_CONNECT_WS_ENDPOINT=ws://127.0.0.1:3002/ npx playwright test
```

### 4. 停止服务

```bash
docker-compose down
```

## 目录结构

```
.
├── Dockerfile              # 自定义 Playwright + VNC 镜像
├── docker-compose.yml      # Docker Compose 配置
├── supervisord.conf        # Supervisor 进程管理配置
├── start-services.sh       # 服务启动脚本
├── tests/                  # 测试文件目录 (挂载到容器)
├── results/                # 测试结果目录 (挂载到容器)
└── README.md              # 使用说明
```

## 配置说明

### VNC 配置
- 分辨率: 1280x800x24
- 无密码访问
- 窗口管理器: Fluxbox

### Docker 配置
- 使用 `--init` 标志避免僵尸进程
- 使用 `--ipc=host` 防止 Chromium 内存不足
- 添加 `SYS_ADMIN` 权限支持 Chromium 沙箱

## 故障排除

1. **如果 VNC 连接失败**：
   - 检查端口是否被占用
   - 确认容器已正常启动

2. **如果 Playwright 连接失败**：
   - 检查 Playwright Server 是否正常运行
   - 确认端口映射正确

3. **如果浏览器启动失败**：
   - 检查容器日志: `docker-compose logs`
   - 确认 DISPLAY 环境变量设置正确
