// 简单的 Google 测试脚本
// 使用容器内预安装的 Playwright

const { spawn } = require('child_process');
const fs = require('fs');

async function runPlaywrightScript() {
  console.log('🚀 开始 Google 测试...');
  
  // 创建 Playwright 脚本内容
  const playwrightScript = `
const { chromium } = require('playwright');

(async () => {
  console.log('连接到 Playwright Server...');
  
  // 连接到本地 Playwright Server
  const browser = await chromium.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    console.log('已连接到浏览器');
    
    // 创建新页面
    const page = await browser.newPage();
    console.log('已创建新页面');
    
    // 设置视窗大小
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 访问 Google
    console.log('正在访问 Google...');
    await page.goto('https://www.google.com', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log('Google 页面已加载');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 截图
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = \`/home/<USER>/results/google-\${timestamp}.png\`;
    
    console.log('正在截图...');
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true 
    });
    
    console.log(\`截图已保存: \${screenshotPath}\`);
    
    // 获取页面标题
    const title = await page.title();
    console.log(\`页面标题: \${title}\`);
    
    // 等待 10 秒
    console.log('等待 10 秒...');
    await page.waitForTimeout(10000);
    
    console.log('✅ 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 关闭浏览器
    await browser.close();
    console.log('浏览器已关闭');
  }
})();
`;

  // 将脚本写入临时文件
  const tempScriptPath = '/tmp/playwright-google-test.js';
  fs.writeFileSync(tempScriptPath, playwrightScript);
  
  console.log('正在运行 Playwright 脚本...');
  
  // 使用 npx 运行 Playwright 脚本
  return new Promise((resolve, reject) => {
    const child = spawn('npx', ['playwright', 'install'], {
      stdio: 'inherit',
      env: { ...process.env, DISPLAY: ':99' }
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log('Playwright 浏览器已安装');
        
        // 运行实际的测试脚本
        const testChild = spawn('node', [tempScriptPath], {
          stdio: 'inherit',
          env: { ...process.env, DISPLAY: ':99' }
        });
        
        testChild.on('close', (testCode) => {
          // 清理临时文件
          try {
            fs.unlinkSync(tempScriptPath);
          } catch (e) {
            // 忽略清理错误
          }
          
          if (testCode === 0) {
            resolve();
          } else {
            reject(new Error(`测试脚本退出码: ${testCode}`));
          }
        });
        
      } else {
        reject(new Error(`Playwright 安装失败，退出码: ${code}`));
      }
    });
  });
}

// 运行测试
runPlaywrightScript()
  .then(() => {
    console.log('🎉 所有测试完成！');
  })
  .catch((error) => {
    console.error('💥 测试失败:', error);
    process.exit(1);
  });
