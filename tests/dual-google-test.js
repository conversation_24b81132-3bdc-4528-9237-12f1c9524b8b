const { chromium } = require('playwright');

async function runGoogleTestOnInstance(instanceNumber, wsEndpoint) {
  console.log(`\n=== 实例 ${instanceNumber} 开始测试 ===`);
  
  try {
    // 连接到指定的 Playwright 服务器
    const browser = await chromium.connect(wsEndpoint);
    console.log(`实例 ${instanceNumber}: 已连接到 Playwright Server`);
    
    // 创建新页面
    const page = await browser.newPage();
    console.log(`实例 ${instanceNumber}: 已创建新页面`);
    
    // 设置视窗大小
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 访问 Google
    console.log(`实例 ${instanceNumber}: 正在访问 Google...`);
    await page.goto('https://www.google.com', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log(`实例 ${instanceNumber}: Google 页面已加载`);
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 截图
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = `/home/<USER>/results/google-instance${instanceNumber}-${timestamp}.png`;
    
    console.log(`实例 ${instanceNumber}: 正在截图...`);
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true 
    });
    
    console.log(`实例 ${instanceNumber}: 截图已保存: ${screenshotPath}`);
    
    // 获取页面标题和 URL
    const title = await page.title();
    const url = page.url();
    console.log(`实例 ${instanceNumber}: 页面标题: ${title}`);
    console.log(`实例 ${instanceNumber}: 页面 URL: ${url}`);
    
    // 等待 10 秒
    console.log(`实例 ${instanceNumber}: 等待 10 秒...`);
    await page.waitForTimeout(10000);
    
    console.log(`实例 ${instanceNumber}: 测试完成！`);
    
    // 关闭浏览器连接
    await browser.close();
    console.log(`实例 ${instanceNumber}: 浏览器连接已关闭`);
    
    return { success: true, instance: instanceNumber };
    
  } catch (error) {
    console.error(`实例 ${instanceNumber}: 测试过程中发生错误:`, error);
    return { success: false, instance: instanceNumber, error: error.message };
  }
}

async function dualGoogleTest() {
  console.log('🚀 开始双实例 Google 测试...');
  console.log('这将同时在两个 Playwright 实例上运行测试');
  
  const startTime = Date.now();
  
  // 并行运行两个实例的测试
  const results = await Promise.allSettled([
    runGoogleTestOnInstance(1, 'ws://localhost:3001/'),
    runGoogleTestOnInstance(2, 'ws://localhost:3002/')
  ]);
  
  const endTime = Date.now();
  const totalTime = (endTime - startTime) / 1000;
  
  console.log('\n🏁 测试结果总结:');
  console.log('==================');
  
  results.forEach((result, index) => {
    const instanceNumber = index + 1;
    if (result.status === 'fulfilled') {
      const testResult = result.value;
      if (testResult.success) {
        console.log(`✅ 实例 ${instanceNumber}: 测试成功`);
      } else {
        console.log(`❌ 实例 ${instanceNumber}: 测试失败 - ${testResult.error}`);
      }
    } else {
      console.log(`❌ 实例 ${instanceNumber}: 测试异常 - ${result.reason}`);
    }
  });
  
  console.log(`\n⏱️  总耗时: ${totalTime.toFixed(2)} 秒`);
  console.log('📁 截图文件保存在 results/ 目录中');
  console.log('\n💡 提示: 你可以通过以下方式查看截图:');
  console.log('   - 在 NoVNC 界面中打开文件管理器');
  console.log('   - 或使用: docker exec playwright-vnc-1 ls -la /home/<USER>/results/');
}

// 运行双实例测试
dualGoogleTest().catch(console.error);
