const { chromium } = require('playwright');

async function googleTest() {
  console.log('🚀 开始 Google 测试...');
  
  try {
    // 直接启动浏览器而不是连接到远程服务器
    console.log('正在启动浏览器...');
    const browser = await chromium.launch({
      headless: false,  // 设置为 false 以便在 VNC 中看到浏览器
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });
    
    console.log('✅ 浏览器已启动');
    
    // 创建新页面
    const page = await browser.newPage();
    console.log('✅ 已创建新页面');
    
    // 设置视窗大小
    await page.setViewportSize({ width: 1920, height: 1080 });
    console.log('✅ 已设置视窗大小');
    
    // 访问 Google
    console.log('🌐 正在访问 Google...');
    await page.goto('https://www.google.com', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log('✅ Google 页面已加载');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    console.log('✅ 页面加载完成');
    
    // 截图
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = `/home/<USER>/results/google-${timestamp}.png`;
    
    console.log('📸 正在截图...');
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true 
    });
    
    console.log(`✅ 截图已保存: ${screenshotPath}`);
    
    // 获取页面标题
    const title = await page.title();
    console.log(`📄 页面标题: ${title}`);
    
    // 获取页面 URL
    const url = page.url();
    console.log(`🔗 页面 URL: ${url}`);
    
    // 等待 10 秒
    console.log('⏰ 等待 10 秒...');
    await page.waitForTimeout(10000);
    
    console.log('🎉 测试完成！');
    
    // 关闭浏览器
    await browser.close();
    console.log('✅ 浏览器已关闭');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
}

// 运行测试
googleTest()
  .then(() => {
    console.log('🏁 Google 测试成功完成！');
    console.log('💡 提示: 你可以通过 NoVNC 界面 (http://localhost:6901) 观察浏览器运行过程');
    console.log('📁 截图文件保存在 results/ 目录中');
  })
  .catch((error) => {
    console.error('💥 测试失败:', error);
    process.exit(1);
  });
