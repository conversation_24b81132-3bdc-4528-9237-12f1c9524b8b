#!/usr/bin/env python3
"""
Python 脚本：使用 WebSocket 连接到 Playwright Server
开启 Google 页面，截图，然后等待10秒
"""

import asyncio
import time
from datetime import datetime
from playwright.async_api import async_playwright

async def google_test_ws():
    """使用 WebSocket 连接到 Playwright Server 进行 Google 测试"""
    print("🚀 开始 Python Google 测试 (WebSocket 连接)...")
    
    async with async_playwright() as p:
        try:
            # 连接到远程 Playwright Server (实例 1)
            print("🔗 正在连接到 Playwright Server...")
            browser = await p.chromium.connect_over_cdp("ws://localhost:3001/")
            print("✅ 已连接到 Playwright Server")
            
            # 创建新页面
            page = await browser.new_page()
            print("✅ 已创建新页面")
            
            # 设置视窗大小
            await page.set_viewport_size({"width": 1280, "height": 800})
            print("✅ 已设置视窗大小 (1280x800)")
            
            # 访问 Google
            print("🌐 正在访问 Google...")
            await page.goto('https://www.google.com', wait_until='networkidle', timeout=30000)
            print("✅ Google 页面已加载")
            
            # 等待页面完全加载
            await page.wait_for_load_state('networkidle')
            print("✅ 页面加载完成")
            
            # 截图
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_path = f"/home/<USER>/results/google-python-{timestamp}.png"
            
            print("📸 正在截图...")
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"✅ 截图已保存: {screenshot_path}")
            
            # 获取页面信息
            title = await page.title()
            url = page.url
            print(f"📄 页面标题: {title}")
            print(f"🔗 页面 URL: {url}")
            
            # 等待 10 秒
            print("⏰ 等待 10 秒...")
            await asyncio.sleep(10)
            
            print("🎉 测试完成！")
            
            # 关闭浏览器连接
            await browser.close()
            print("✅ 浏览器连接已关闭")
            
            return True
            
        except Exception as error:
            print(f"❌ 测试过程中发生错误: {error}")
            return False

async def google_test_direct():
    """直接启动浏览器进行 Google 测试（备用方案）"""
    print("🚀 开始 Python Google 测试 (直接启动浏览器)...")
    
    async with async_playwright() as p:
        try:
            # 直接启动浏览器
            print("🔗 正在启动浏览器...")
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            print("✅ 浏览器已启动")
            
            # 创建新页面
            page = await browser.new_page()
            print("✅ 已创建新页面")
            
            # 设置视窗大小
            await page.set_viewport_size({"width": 1280, "height": 800})
            print("✅ 已设置视窗大小 (1280x800)")
            
            # 访问 Google
            print("🌐 正在访问 Google...")
            await page.goto('https://www.google.com', wait_until='networkidle', timeout=30000)
            print("✅ Google 页面已加载")
            
            # 等待页面完全加载
            await page.wait_for_load_state('networkidle')
            print("✅ 页面加载完成")
            
            # 截图
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_path = f"/home/<USER>/results/google-python-direct-{timestamp}.png"
            
            print("📸 正在截图...")
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"✅ 截图已保存: {screenshot_path}")
            
            # 获取页面信息
            title = await page.title()
            url = page.url
            print(f"📄 页面标题: {title}")
            print(f"🔗 页面 URL: {url}")
            
            # 等待 10 秒
            print("⏰ 等待 10 秒...")
            await asyncio.sleep(10)
            
            print("🎉 测试完成！")
            
            # 关闭浏览器
            await browser.close()
            print("✅ 浏览器已关闭")
            
            return True
            
        except Exception as error:
            print(f"❌ 测试过程中发生错误: {error}")
            return False

async def main():
    """主函数：尝试 WebSocket 连接，失败则使用直接启动方式"""
    print("🐍 Python Playwright Google 测试开始")
    print("=" * 50)
    
    # 首先尝试 WebSocket 连接
    print("📡 尝试使用 WebSocket 连接到 Playwright Server...")
    ws_success = await google_test_ws()
    
    if not ws_success:
        print("\n🔄 WebSocket 连接失败，尝试直接启动浏览器...")
        direct_success = await google_test_direct()
        
        if not direct_success:
            print("💥 所有测试方法都失败了")
            return False
    
    print("\n🏁 Python Google 测试成功完成！")
    print("💡 提示: 你可以通过 NoVNC 界面 (http://localhost:6901) 观察浏览器运行过程")
    print("📁 截图文件保存在 results/ 目录中")
    return True

if __name__ == "__main__":
    # 运行异步主函数
    success = asyncio.run(main())
    
    if success:
        print("✅ 脚本执行成功")
        exit(0)
    else:
        print("❌ 脚本执行失败")
        exit(1)
