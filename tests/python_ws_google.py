#!/usr/bin/env python3
"""
Python 脚本：使用 WebSocket 连接到 Playwright Server
开启 Google 页面，截图，然后等待10秒
"""

import asyncio
from datetime import datetime

async def run_google_test_ws():
    """使用 WebSocket 连接到 Playwright Server 运行 Google 测试"""
    try:
        # 动态导入 playwright
        from playwright.async_api import async_playwright
        
        print("🚀 开始 Python Google 测试 (WebSocket 连接)...")
        print("🔗 正在连接到 Playwright Server (ws://127.0.0.1:3001/)...")
        
        async with async_playwright() as p:
            # 连接到远程 Playwright Server
            # 注意：使用 127.0.0.1 而不是 localhost 避免 IPv6 问题
            browser = await p.chromium.connect_over_cdp("ws://127.0.0.1:3001/")
            print("✅ 已连接到 Playwright Server")
            
            # 创建新页面
            page = await browser.new_page()
            print("✅ 已创建新页面")
            
            # 设置视窗大小
            await page.set_viewport_size({"width": 1280, "height": 800})
            print("✅ 已设置视窗大小")
            
            # 访问 Google
            print("🌐 正在访问 Google...")
            await page.goto('https://www.google.com', wait_until='networkidle')
            print("✅ Google 页面已加载")
            
            # 截图
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_path = f"/home/<USER>/results/google-python-ws-{timestamp}.png"
            
            print("📸 正在截图...")
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"✅ 截图已保存: {screenshot_path}")
            
            # 获取页面信息
            title = await page.title()
            print(f"📄 页面标题: {title}")
            print(f"🔗 页面 URL: {page.url}")
            
            # 等待 10 秒
            print("⏰ 等待 10 秒...")
            await asyncio.sleep(10)
            
            print("🎉 测试完成！")
            
            # 关闭连接
            await browser.close()
            print("✅ WebSocket 连接已关闭")
            
    except ImportError:
        print("❌ 错误: 未找到 playwright 模块")
        print("💡 请先安装: pip install playwright")
        return False
    except Exception as error:
        print(f"❌ 测试失败: {error}")
        return False
    
    return True

if __name__ == "__main__":
    print("🐍 Python WebSocket Google 测试")
    print("=" * 40)
    
    # 运行测试
    success = asyncio.run(run_google_test_ws())
    
    if success:
        print("\n🏁 WebSocket 测试成功完成！")
        print("💡 提示: 可通过 http://localhost:6901 观察浏览器")
    else:
        print("\n💥 WebSocket 测试失败")
        exit(1)
