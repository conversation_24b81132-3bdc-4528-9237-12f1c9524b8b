#!/usr/bin/env python3
"""
简化版 Python 脚本：使用 WebSocket 连接到 Playwright Server
开启 Google 页面，截图，然后等待10秒
"""

import asyncio
import time
from datetime import datetime


async def run_google_test():
    """运行 Google 测试"""
    try:
        # 动态导入 playwright
        from playwright.async_api import async_playwright

        print("🚀 开始 Python Google 测试...")
        print("🔗 正在连接到 Playwright Server...")

        async with async_playwright() as p:
            # 直接启动浏览器而不是连接到远程服务器
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                ],
            )
            print("✅ 浏览器已启动")

            # 创建新页面
            page = await browser.new_page()
            print("✅ 已创建新页面")

            # 设置视窗大小
            await page.set_viewport_size({"width": 1280, "height": 800})
            print("✅ 已设置视窗大小")

            # 访问 Google
            print("🌐 正在访问 Google...")
            await page.goto("https://www.google.com", wait_until="networkidle")
            print("✅ Google 页面已加载")

            # 截图
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_path = f"/home/<USER>/results/google-python-ws-{timestamp}.png"

            print("📸 正在截图...")
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"✅ 截图已保存: {screenshot_path}")

            # 获取页面信息
            title = await page.title()
            print(f"📄 页面标题: {title}")
            print(f"🔗 页面 URL: {page.url}")

            # 等待 10 秒
            print("⏰ 等待 10 秒...")
            await asyncio.sleep(10)

            print("🎉 测试完成！")

            # 关闭浏览器
            await browser.close()
            print("✅ 浏览器已关闭")

    except ImportError:
        print("❌ 错误: 未找到 playwright 模块")
        print("💡 请先安装: pip install playwright")
        return False
    except Exception as error:
        print(f"❌ 测试失败: {error}")
        return False

    return True


if __name__ == "__main__":
    print("🐍 Python Google 测试")
    print("=" * 30)

    # 运行测试
    success = asyncio.run(run_google_test())

    if success:
        print("\n🏁 测试成功完成！")
        print("💡 提示: 可通过 http://localhost:6901 观察浏览器")
    else:
        print("\n💥 测试失败")
        exit(1)
