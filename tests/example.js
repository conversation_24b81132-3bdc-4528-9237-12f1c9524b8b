const { chromium } = require('playwright');

async function runTest() {
  // 连接到远程 Playwright 服务器
  const browser = await chromium.connect('ws://localhost:3001/');
  
  try {
    const page = await browser.newPage();
    
    // 访问测试页面
    await page.goto('https://playwright.dev');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 截图
    await page.screenshot({ path: '/home/<USER>/results/example-screenshot.png' });
    
    // 获取页面标题
    const title = await page.title();
    console.log('页面标题:', title);
    
    // 点击链接
    await page.click('text=Get started');
    await page.waitForLoadState('networkidle');
    
    // 再次截图
    await page.screenshot({ path: '/home/<USER>/results/get-started-screenshot.png' });
    
    console.log('测试完成！');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await browser.close();
  }
}

runTest();
