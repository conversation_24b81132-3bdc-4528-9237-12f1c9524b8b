// 使用动态导入来加载 Playwright
async function loadPlaywright() {
  try {
    // 尝试直接 require
    return require('playwright');
  } catch (error) {
    console.log('正在安装 Playwright...');
    const { execSync } = require('child_process');
    execSync('npm install playwright', { stdio: 'inherit' });
    return require('playwright');
  }
}

async function googleTest() {
  console.log('开始 Google 测试...');

  // 加载 Playwright
  const playwright = await loadPlaywright();
  const { chromium } = playwright;

  // 连接到远程 Playwright 服务器 (实例 1)
  const browser = await chromium.connect('ws://localhost:3001/');

  try {
    console.log('已连接到 Playwright Server');

    // 创建新页面
    const page = await browser.newPage();
    console.log('已创建新页面');

    // 设置视窗大小
    await page.setViewportSize({ width: 1920, height: 1080 });

    // 访问 Google
    console.log('正在访问 Google...');
    await page.goto('https://www.google.com', {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    console.log('Google 页面已加载');

    // 等待页面完全加载
    await page.waitForLoadState('networkidle');

    // 截图
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = `/home/<USER>/results/google-screenshot-${timestamp}.png`;

    console.log('正在截图...');
    await page.screenshot({
      path: screenshotPath,
      fullPage: true
    });

    console.log(`截图已保存: ${screenshotPath}`);

    // 获取页面标题
    const title = await page.title();
    console.log(`页面标题: ${title}`);

    // 等待 10 秒
    console.log('等待 10 秒...');
    await page.waitForTimeout(10000);

    console.log('测试完成！');

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    // 关闭浏览器连接
    await browser.close();
    console.log('浏览器连接已关闭');
  }
}

// 运行测试
googleTest().catch(console.error);
